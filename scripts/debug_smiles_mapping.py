#!/usr/bin/env python3
"""
Debug script for SMILES mapping logic in OptimizedBrainAPIProcessor

This script creates a minimal test case to trace SMILES through the entire
processing pipeline and identify where the "No API result found" errors occur.
"""

import asyncio
import sys
import os
import pandas as pd
import logging
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_config import PipelineConfig
from modular_pipeline import OptimizedBrainAPIProcessor, PersistentCache
from update_csv_with_brain_api import CSVUpdater as BrainAPIUpdater


class SMILESMappingDebugger:
    """Debug SMILES mapping issues in the optimized processor"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        
    def _setup_logging(self):
        """Setup detailed logging for debugging"""
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('debug_smiles_mapping.log')
            ]
        )
        return logging.getLogger(__name__)
    
    async def test_smiles_mapping(self):
        """Test SMILES mapping with a small sample"""
        
        print("🧪 Testing SMILES Mapping Logic")
        print("=" * 60)
        
        # Create test data with known SMILES
        test_data = [
            {'canonical_smiles': 'CCO', 'other_field': 'ethanol'},
            {'canonical_smiles': 'CC(=O)O', 'other_field': 'acetic_acid'},
            {'canonical_smiles': 'C1=CC=CC=C1', 'other_field': 'benzene'},
            {'canonical_smiles': 'CCO', 'other_field': 'ethanol_duplicate'},  # Duplicate
            {'canonical_smiles': '  CCO  ', 'other_field': 'ethanol_whitespace'},  # Whitespace
            {'canonical_smiles': '', 'other_field': 'empty_smiles'},  # Empty
            {'canonical_smiles': None, 'other_field': 'null_smiles'},  # Null
            {'canonical_smiles': 'INVALID<>SMILES', 'other_field': 'invalid'},  # Invalid
        ]
        
        df = pd.DataFrame(test_data)
        print(f"📊 Test DataFrame created with {len(df)} rows:")
        for i, row in df.iterrows():
            print(f"  Row {i}: '{row['canonical_smiles']}'")
        
        # Setup test environment
        config = PipelineConfig(
            input_file=Path("test.jsonl"),
            output_dir=Path("./debug_output"),
            temp_dir=Path("./debug_temp"),
            log_dir=Path("./debug_logs"),
            brain_api_batch_size=100,
            cache_ttl_hours=1,
        )
        
        # Create cache and processor
        cache_dir = Path("./debug_cache")
        cache = PersistentCache(cache_dir, 'debug_brain_cache', 1)
        brain_updater = BrainAPIUpdater(batch_size=100)
        
        processor = OptimizedBrainAPIProcessor(
            brain_updater=brain_updater,
            cache=cache,
            batch_size=100,
            max_concurrent=1  # Single threaded for debugging
        )
        
        print("\n🔍 Step 1: Extract and Validate SMILES")
        print("-" * 40)
        
        unique_smiles, smiles_mapping = processor._extract_and_validate_smiles(df)
        
        print(f"Unique SMILES found: {len(unique_smiles)}")
        print(f"SMILES mapping entries: {len(smiles_mapping)}")
        
        for smiles, indices in smiles_mapping.items():
            print(f"  '{smiles}' -> rows {indices}")
        
        print("\n🔍 Step 2: Check Cache")
        print("-" * 40)
        
        cached_results = cache.get_multiple(unique_smiles)
        missing_smiles = cache.get_missing_keys(unique_smiles)
        
        print(f"Cached results: {len(cached_results)}")
        print(f"Missing from cache: {len(missing_smiles)}")
        print(f"Missing SMILES: {missing_smiles}")
        
        print("\n🔍 Step 3: Process Missing SMILES (Simulated)")
        print("-" * 40)
        
        # Simulate API results for debugging
        simulated_results = {}
        for smiles in missing_smiles:
            if smiles and smiles != 'INVALID<>SMILES':  # Simulate successful processing
                simulated_results[smiles] = {
                    'canonical_smiles': smiles,
                    'inchified_smiles': f'InChI=1S/{smiles}',
                    'success': True,
                    'error_category': None
                }
            else:
                simulated_results[smiles] = {
                    'canonical_smiles': smiles,
                    'inchified_smiles': None,
                    'success': False,
                    'error': 'Invalid SMILES format',
                    'error_category': 'invalid_smiles'
                }
        
        print(f"Simulated {len(simulated_results)} results:")
        for smiles, result in simulated_results.items():
            status = "SUCCESS" if result['success'] else "FAILED"
            print(f"  '{smiles}' -> {status}")
        
        print("\n🔍 Step 4: Combine Results")
        print("-" * 40)
        
        all_results = {**cached_results, **simulated_results}
        print(f"Total results: {len(all_results)}")
        
        print("\n🔍 Step 5: Apply Results to DataFrame")
        print("-" * 40)
        
        updated_df, error_records = processor._apply_results_to_dataframe_enhanced(
            df, all_results, smiles_mapping
        )
        
        print(f"Updated rows: {len(updated_df)}")
        print(f"Error records: {len(error_records)}")
        
        print("\n📋 Detailed Results:")
        print("=" * 60)
        
        print("\n✅ Successfully Updated Rows:")
        if not updated_df.empty:
            for i, row in updated_df.iterrows():
                print(f"  Original Row {i}: '{row['canonical_smiles']}' -> '{row.get('inchified_smiles', 'N/A')}'")
        else:
            print("  None")
        
        print("\n❌ Error Records:")
        for i, error in enumerate(error_records):
            original_idx = error.get('original_index', 'unknown')
            smiles = error.get('canonical_smiles', 'N/A')
            reason = error.get('error_reason', 'N/A')
            category = error.get('error_category', 'N/A')
            print(f"  Error {i}: Row {original_idx}, SMILES '{smiles}', Reason: {reason}, Category: {category}")
        
        print("\n🔍 Step 6: Verification")
        print("-" * 40)
        
        total_processed = len(updated_df) + len(error_records)
        original_count = len(df)
        
        print(f"Original rows: {original_count}")
        print(f"Total processed: {total_processed}")
        print(f"Difference: {original_count - total_processed}")
        
        if original_count == total_processed:
            print("✅ All rows accounted for!")
        else:
            print("❌ Row count mismatch - some rows lost!")
        
        # Check for "No API result found" errors
        no_result_errors = [e for e in error_records if 'No API result found' in e.get('error_reason', '')]
        if no_result_errors:
            print(f"\n⚠️ Found {len(no_result_errors)} 'No API result found' errors:")
            for error in no_result_errors:
                smiles = error.get('canonical_smiles', 'N/A')
                print(f"  SMILES: '{smiles}'")
                
                # Debug: Check if SMILES was in mapping and results
                in_mapping = smiles in smiles_mapping
                in_results = smiles in all_results
                print(f"    In mapping: {in_mapping}, In results: {in_results}")
        else:
            print("✅ No 'No API result found' errors!")
        
        return {
            'original_count': original_count,
            'updated_count': len(updated_df),
            'error_count': len(error_records),
            'no_result_errors': len(no_result_errors),
            'smiles_mapping': smiles_mapping,
            'results': all_results
        }
    
    async def compare_with_original(self):
        """Compare optimized processor with original implementation"""
        print("\n🔄 Comparing with Original Implementation")
        print("=" * 60)
        
        # This would require implementing a comparison with the original
        # non-optimized version to identify differences
        print("TODO: Implement comparison with original processor")


async def main():
    """Main debugging function"""
    debugger = SMILESMappingDebugger()
    
    try:
        results = await debugger.test_smiles_mapping()
        
        print("\n📊 Summary:")
        print("=" * 60)
        print(f"Original rows: {results['original_count']}")
        print(f"Successfully updated: {results['updated_count']}")
        print(f"Errors: {results['error_count']}")
        print(f"'No API result found' errors: {results['no_result_errors']}")
        
        if results['no_result_errors'] == 0:
            print("\n🎉 SUCCESS: No 'No API result found' errors detected!")
        else:
            print(f"\n⚠️ ISSUE: {results['no_result_errors']} 'No API result found' errors found")
            print("Check the detailed output above for debugging information.")
        
    except Exception as e:
        print(f"\n❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
