#!/usr/bin/env python3
"""
Test script to verify the fixes for "No API result found" errors
"""

import asyncio
import sys
import os
import pandas as pd
import logging
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_config import PipelineConfig
from modular_pipeline import OptimizedBrainAPIProcessor, PersistentCache
from update_csv_with_brain_api import CSVUpdater as BrainAPIUpdater


async def test_fixed_pipeline():
    """Test the fixed pipeline with a small sample"""
    
    print("🧪 Testing Fixed Optimized Pipeline")
    print("=" * 50)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Create test data
    test_data = [
        {'canonical_smiles': 'CCO'},
        {'canonical_smiles': 'CC(=O)O'},
        {'canonical_smiles': 'C1=CC=CC=C1'},
        {'canonical_smiles': 'CCO'},  # Duplicate
        {'canonical_smiles': '  CCO  '},  # Whitespace
    ]
    
    df = pd.DataFrame(test_data)
    print(f"📊 Test data: {len(df)} rows")
    
    # Setup processor
    config = PipelineConfig(
        input_file=Path("test.jsonl"),
        output_dir=Path("./test_output"),
        temp_dir=Path("./test_temp"),
        log_dir=Path("./test_logs"),
        brain_api_batch_size=100,
    )
    
    cache_dir = Path("./test_cache")
    cache = PersistentCache(cache_dir, 'test_brain_cache', 1)
    brain_updater = BrainAPIUpdater(batch_size=100)
    
    processor = OptimizedBrainAPIProcessor(
        brain_updater=brain_updater,
        cache=cache,
        batch_size=100,
        max_concurrent=1
    )
    
    print("\n🔍 Testing SMILES normalization...")
    
    # Test normalization
    test_smiles = ['CCO', '  CCO  ', 'CC(=O)O', None, '']
    for smiles in test_smiles:
        normalized = processor._normalize_smiles(smiles)
        print(f"  '{smiles}' -> '{normalized}'")
    
    print("\n🔍 Testing SMILES extraction and validation...")
    
    unique_smiles, smiles_mapping = processor._extract_and_validate_smiles(df)
    
    print(f"Unique SMILES: {unique_smiles}")
    print(f"SMILES mapping: {smiles_mapping}")
    
    # Simulate results for all unique SMILES
    simulated_results = {}
    for smiles in unique_smiles:
        simulated_results[smiles] = {
            'canonical_smiles': smiles,
            'inchified_smiles': f'InChI=1S/{smiles}',
            'success': True,
            'error_category': None
        }
    
    print(f"\nSimulated results for {len(simulated_results)} SMILES")
    
    print("\n🔍 Testing result application...")
    
    updated_df, error_records = processor._apply_results_to_dataframe_enhanced(
        df, simulated_results, smiles_mapping
    )
    
    print(f"✅ Updated rows: {len(updated_df)}")
    print(f"❌ Error records: {len(error_records)}")
    
    # Check for "No API result found" errors
    no_result_errors = [e for e in error_records if 'No API result found' in e.get('error_reason', '')]
    
    if no_result_errors:
        print(f"\n⚠️ Found {len(no_result_errors)} 'No API result found' errors!")
        for error in no_result_errors:
            print(f"  Error: {error}")
        return False
    else:
        print("\n🎉 SUCCESS: No 'No API result found' errors!")
        return True


async def main():
    """Main test function"""
    try:
        success = await test_fixed_pipeline()
        if success:
            print("\n✅ All tests passed! The fixes appear to be working.")
        else:
            print("\n❌ Tests failed. There are still issues to fix.")
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
